'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { formatDistanceToNow } from 'date-fns';
import { it } from 'date-fns/locale'; // Import Italian locale properly
import { MessageSquare, AlertCircle, Clock, CheckCircle2, Copy, Check } from 'lucide-react';
import { SupportChat } from './SupportChatList';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";

// Enhanced interface with proper typing
interface ChatCardProps {
  chat: SupportChat;
  onClick?: () => void;
  isSelected?: boolean;
}

// Type for priority levels
type PriorityLevel = 'high' | 'medium' | 'low';

// Type for chat status
type ChatStatus = 'pending' | 'in_progress' | 'resolved';

const ChatCard = ({ chat, onClick, isSelected = false }: ChatCardProps) => {
  const router = useRouter();
  const [copied, setCopied] = useState(false);

  const handleCardClick = () => {
    if (onClick) {
      onClick();
    } else {
      // Navigate to individual chat page
      router.push(`/dashboard/chat/${chat.id}`);
    }
  };

  // Generate sophisticated avatar color based on user name hash
  const generateAvatarColor = (name: string): string => {
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
      hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }

    const colors = [
      '#133157', // Primary blue
      '#1e40af', // Blue-700
      '#7c3aed', // Violet-600
      '#dc2626', // Red-600
      '#ea580c', // Orange-600
      '#16a34a', // Green-600
      '#0891b2', // Cyan-600
      '#be185d', // Pink-600
    ];

    return colors[Math.abs(hash) % colors.length];
  };

  // Copy conversation ID to clipboard
  const handleCopyId = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await navigator.clipboard.writeText(chat.id.toString());
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy ID:', err);
    }
  };

  // Truncate email for better display
  const truncateEmail = (email: string, maxLength: number = 25): string => {
    if (email.length <= maxLength) return email;
    return email.substring(0, maxLength) + '...';
  };
  const getPriorityIcon = (priority: PriorityLevel): React.ReactElement | null => {
    switch (priority) {
      case 'high': return <AlertCircle className="h-4 w-4" style={{ color: 'var(--priority-high)' }} />;
      case 'medium': return <AlertCircle className="h-4 w-4" style={{ color: 'var(--priority-medium)' }} />;
      case 'low': return <AlertCircle className="h-4 w-4" style={{ color: 'var(--priority-low)' }} />;
      default: return null;
    }
  };

  const getStatusBadge = (status: ChatStatus): React.ReactElement => {
    const baseClasses = "status-badge inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-semibold uppercase tracking-wide";

    switch (status) {
      case 'pending':
        return (
          <div className={`${baseClasses} status-badge-pending`}>
            <Clock className="h-3 w-3" />
            In Attesa
          </div>
        );
      case 'in_progress':
        return (
          <div className={`${baseClasses} status-badge-in-progress`}>
            <Clock className="h-3 w-3 animate-spin" />
            In Elaborazione
          </div>
        );
      case 'resolved':
        return (
          <div className={`${baseClasses} status-badge-resolved`}>
            <CheckCircle2 className="h-3 w-3" />
            Completata
          </div>
        );
      default:
        return (
          <div className={`${baseClasses} bg-gray-500 text-white`}>
            Sconosciuto
          </div>
        );
    }
  };

  const getPriorityText = (priority: PriorityLevel): string => {
    const priorityMap: { [key: string]: string } = {
      high: 'Priorità Alta',
      medium: 'Priorità Media',
      low: 'Priorità Bassa',
      urgent: 'Urgente'
    };
    return priorityMap[priority] || priority.charAt(0).toUpperCase() + priority.slice(1);
  };

  const getPriorityClass = (priority: PriorityLevel): string => {
    switch (priority) {
      case 'high': return 'priority-high';
      case 'medium': return 'priority-medium';
      case 'low': return 'priority-low';
      default: return 'priority-medium';
    }
  };

  const formatDate = (dateString: string): string => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true, locale: it });
    } catch {
      return 'Data non valida';
    }
  };
  return (
    <Card
      className={`chat-card transition-all duration-300 cursor-pointer h-full
        ${isSelected ? 'ring-2 ring-[#113158] border-[#113158] shadow-xl' : ''}
        ${chat.priority === 'urgent' ? 'border-l-4 border-l-red-600' :
          chat.priority === 'high' ? 'border-l-4 border-l-red-500' :
          chat.priority === 'medium' ? 'border-l-4 border-l-[#febd49]' :
          'border-l-4 border-l-green-500'}
      `}
      onClick={handleCardClick}
      role="button"
      tabIndex={0}
      aria-label={`Conversazione con ${chat.user_name}, stato: ${getPriorityText(chat.priority as PriorityLevel)}`}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleCardClick();
        }
      }}
    >
      {/* User Info Header Section */}
      <CardHeader className="chat-card-header">
        <div className="flex items-center gap-4">
          <Avatar className="chat-avatar" style={{ backgroundColor: generateAvatarColor(chat.user_name) }}>
            <AvatarFallback className="text-white font-semibold bg-transparent">
              {chat.user_name.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg font-semibold text-[#113158] mb-1 truncate">
              {chat.user_name}
            </CardTitle>
            <CardDescription className="text-sm text-gray-600 opacity-70 truncate">
              {truncateEmail(chat.user_email)}
            </CardDescription>
          </div>
          <div className="text-xs text-gray-500 font-medium">
            {formatDate(chat.updated_at)}
          </div>
        </div>
      </CardHeader>

      {/* Priority/Status Middle Section */}
      <CardContent className="chat-card-content">
        <div className="space-y-4">
          {/* Message snippet */}
          <p className="text-sm line-clamp-2 min-h-[40px] text-gray-700 leading-relaxed">
            {chat.message_snippet}
          </p>

          {/* Status and Priority Row */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getStatusBadge(chat.status as ChatStatus)}
            </div>

            <div className={`priority-indicator ${getPriorityClass(chat.priority as PriorityLevel)}`}>
              {getPriorityIcon(chat.priority as PriorityLevel)}
              <span className="text-xs font-semibold">
                {getPriorityText(chat.priority as PriorityLevel)}
              </span>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Action Buttons Footer */}
      <CardFooter className="chat-card-footer flex items-center justify-between">
        <button
          className="action-button action-button-primary"
          onClick={(e) => {
            e.stopPropagation();
            handleCardClick();
          }}
          aria-label="Apri conversazione"
        >
          <MessageSquare size={16} />
          <span>Apri Chat</span>
        </button>

        <button
          className="copy-button"
          onClick={handleCopyId}
          aria-label="Copia ID conversazione"
          title="Copia ID conversazione"
        >
          {copied ? (
            <>
              <Check size={12} />
              <span>Copiato!</span>
            </>
          ) : (
            <>
              <Copy size={12} />
              <span>#{chat.id.toString().substring(0, 8)}...</span>
            </>
          )}
        </button>
      </CardFooter>
    </Card>
  );
};

export default ChatCard;
