'use client';

import React, { useEffect, useState } from 'react';
import ChatCard from '@/components/dashboard/ChatCard';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, AlertTriangle, Inbox } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChatFilters, useChatData } from '@/components/context/ChatDataContext';

export interface SupportChat {
  id: number;
  user_id: string;
  user_email: string;
  user_name: string;
  message_snippet: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'resolved';
  created_at: string;
  updated_at: string;
}

interface SupportChatListProps {
  initialFilters?: ChatFilters;
  onChatSelect?: (chat: SupportChat) => void;
}

const SupportChatList = ({ initialFilters = {}, onChatSelect }: SupportChatListProps) => {
  const router = useRouter();
  const pathname = usePathname();
  
  // Use ChatDataContext instead of local state
  const { 
    chats, 
    loadingChats, 
    chatsError, 
    fetchChats 
  } = useChatData();
  
  const [filters, setFilters] = useState<ChatFilters>({
    ...initialFilters,
    sort_by: initialFilters.sort_by || 'latest'
  });
  // Fetch chats when filters change using the context
  useEffect(() => {
    fetchChats(filters);
  }, [filters, fetchChats]);  // Listen for chat status updates from other components
  useEffect(() => {
    const handleStatusUpdate = () => {
      // Make sure we don't return true here which would indicate async response
      fetchChats(filters, true); // Force refresh
      // Return undefined explicitly (same as no return)
    };
    
    window.addEventListener('chat-status-updated', handleStatusUpdate);
    
    return () => {
      window.removeEventListener('chat-status-updated', handleStatusUpdate);
    };
  }, [filters, fetchChats]);

  // Update URL with filters for bookmarking and sharing
  const updateUrlWithFilters = (updatedFilters: ChatFilters) => {
    const params = new URLSearchParams();
    if (updatedFilters.status) params.set('status', updatedFilters.status);
    if (updatedFilters.priority) params.set('priority', updatedFilters.priority);
    if (updatedFilters.assigned_agent) params.set('agent', updatedFilters.assigned_agent);
    if (updatedFilters.sort_by) params.set('sort', updatedFilters.sort_by);
    
    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  };

  const handleFilterChange = (newFilters: ChatFilters) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    updateUrlWithFilters(updatedFilters);
  };

  const handleRefresh = () => {
    fetchChats(filters, true); // Force refresh
  };

  // Animation variants for list items
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };
  
  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { type: "spring", stiffness: 300, damping: 24 } }
  };
  return (
    <div className="flex flex-col space-y-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6 p-6 rounded-xl bg-gradient-to-r from-[#f8f9fa] to-[#ffffff] border border-[#e5e7eb] shadow-sm">
        <div className="flex-1">
          <div className="flex items-center gap-3 flex-wrap mb-3">
            {filters.status && (
              <Badge className="bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0 px-3 py-1.5 font-semibold text-xs">
                Stato: {filters.status === 'pending' ? 'In Attesa' :
                        filters.status === 'in_progress' ? 'In Elaborazione' : 'Completate'}
              </Badge>
            )}
            {filters.priority && (
              <Badge className="bg-gradient-to-r from-[#febd49] to-[#f59e0b] text-white border-0 px-3 py-1.5 font-semibold text-xs">
                <AlertTriangle className="h-3 w-3 mr-1.5" />
                Priorità: {filters.priority === 'high' ? 'Alta' :
                          filters.priority === 'medium' ? 'Media' :
                          filters.priority === 'low' ? 'Bassa' : 'Urgente'}
              </Badge>
            )}
          </div>
          <div>
            <h2 className="text-2xl font-bold text-[#113158] mb-2">
              {loadingChats ? 'Caricamento conversazioni...' : 'Conversazioni Trovate'}
            </h2>
            <p className="text-base text-gray-600 leading-relaxed">
              {loadingChats ? 'Attendere prego...' :
               `${chats.length} ${chats.length === 1 ? 'conversazione disponibile' : 'conversazioni disponibili'}`}
            </p>
          </div>
        </div>

        <Button
          onClick={handleRefresh}
          variant="outline"
          disabled={loadingChats}
          className="action-button action-button-primary h-12 px-6 gap-2 border-2 border-[#febd49]/30 hover:bg-[#febd49]/10 hover:text-[#113158] font-medium rounded-lg transition-all duration-200"
          aria-label="Aggiorna lista conversazioni"
        >
          {loadingChats ?
            <div className="w-4 h-4 border-2 border-t-transparent border-[#113158] rounded-full animate-spin"></div> :
            <RefreshCw className="h-4 w-4" />
          }
          {loadingChats ? 'Aggiornamento...' : 'Aggiorna Lista'}
        </Button>
      </div>      <AnimatePresence mode="wait">
        {loadingChats ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="chat-grid"
          >
            {/* Skeleton Loading Cards */}
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="min-h-[280px]">
                <div className="chat-card animate-pulse">
                  <div className="chat-card-header">
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                      <div className="flex-1">
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                      <div className="h-3 bg-gray-200 rounded w-16"></div>
                    </div>
                  </div>
                  <div className="chat-card-content">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <div className="h-3 bg-gray-200 rounded w-full"></div>
                        <div className="h-3 bg-gray-200 rounded w-4/5"></div>
                      </div>
                      <div className="flex justify-between">
                        <div className="h-6 bg-gray-200 rounded w-20"></div>
                        <div className="h-6 bg-gray-200 rounded w-24"></div>
                      </div>
                    </div>
                  </div>
                  <div className="chat-card-footer">
                    <div className="h-8 bg-gray-200 rounded w-24"></div>
                    <div className="h-6 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
              </div>
            ))}
          </motion.div>
        ) : chatsError ? (
          <motion.div
            key="error"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="bg-gradient-to-br from-red-50 to-red-100 border-2 border-red-200 p-12 my-4 text-red-700 rounded-xl flex flex-col items-center justify-center min-h-[350px] shadow-lg"
          >
            <AlertTriangle className="w-16 h-16 mb-6 text-red-500" />
            <h4 className="text-xl font-bold mb-2">Errore di Caricamento</h4>
            <p className="text-lg font-medium text-center mb-6 max-w-md">{chatsError}</p>
            <Button
              variant="outline"
              className="border-2 border-red-300 hover:bg-red-100 text-red-700 font-medium px-6 py-3 rounded-lg"
              onClick={handleRefresh}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Riprova Caricamento
            </Button>
          </motion.div>
        ) : chats.length === 0 ? (
          <motion.div
            key="empty"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="bg-white rounded-xl p-16 text-center shadow-lg border-2 border-[#e5e7eb] flex flex-col items-center min-h-[350px] justify-center"
          >
            <div className="p-6 rounded-full bg-gradient-to-br from-[#f8f9fa] to-[#e5e7eb] mb-6">
              <Inbox className="h-12 w-12 text-[#113158]/60" />
            </div>
            <h4 className="text-2xl font-bold text-[#113158] mb-3">Nessuna Conversazione Trovata</h4>
            <p className="text-gray-600 max-w-md text-lg leading-relaxed mb-6">
              {filters.status || filters.priority
                ? "Non ci sono conversazioni che corrispondono ai filtri selezionati. Prova a modificare i criteri di ricerca."
                : "Al momento non ci sono conversazioni di supporto attive. Le nuove richieste appariranno qui automaticamente."}
            </p>
            {(filters.status || filters.priority) && (
              <Button
                variant="outline"
                onClick={() => handleFilterChange({ status: undefined, priority: undefined })}
                className="border-2 border-[#febd49]/30 hover:bg-[#febd49]/10 text-[#113158] font-medium px-6 py-3 rounded-lg"
              >
                Rimuovi Tutti i Filtri
              </Button>
            )}
          </motion.div>
        ) : (
          <motion.div
            key="chatlist"
            variants={container}
            initial="hidden"
            animate="show"
            className="chat-grid"
          >
            {chats.map(chat => (
              <motion.div key={chat.id} variants={item} className="min-h-[280px]">
                <ChatCard
                  chat={chat}
                  onClick={() => {
                    if (onChatSelect) {
                      onChatSelect(chat);
                    } else {
                      router.push(`/dashboard/chat/${chat.id}`);
                    }
                  }}
                />
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SupportChatList;
